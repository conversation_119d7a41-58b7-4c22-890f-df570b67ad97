/**
 * Created on 2025/4/27
 */
package silkui.components.icon

import ohos.font.*
import std.collection.HashMap

public let IconNames: HashMap<String, String> = HashMap<String, String>([
    ("warning-o", "\u{e776}"),
    ("wechat-pay", "\u{e777}"),
    ("shrink", "\u{e778}"),
    ("star", "\u{e779}"),
    ("youzan-shield", "\u{e77a}"),
    ("thumb-circle", "\u{e77b}"),
    ("todo-list-o", "\u{e77c}"),
    ("thumb-circle-o", "\u{e77d}"),
    ("wechat", "\u{e77e}"),
    ("upgrade", "\u{e77f}"),
    ("shield-o", "\u{e780}"),
    ("invitation", "\u{e781}"),
    ("guide-o", "\u{e782}"),
    ("add-square", "\u{e693}"),
    ("add", "\u{e694}"),
    ("add-o", "\u{e695}"),
    ("arrow-left", "\u{e696}"),
    ("aim", "\u{e697}"),
    ("arrow", "\u{e698}"),
    ("arrow-up", "\u{e699}"),
    ("arrow-down", "\u{e69a}"),
    ("alipay", "\u{e69b}"),
    ("ascending", "\u{e69c}"),
    ("after-sale", "\u{e69d}"),
    ("bag-o", "\u{e69e}"),
    ("audio", "\u{e69f}"),
    ("back-top", "\u{e6a0}"),
    ("award", "\u{e6a1}"),
    ("bag", "\u{e6a2}"),
    ("award-o", "\u{e6a3}"),
    ("balance-list", "\u{e6a4}"),
    ("balance-o", "\u{e6a5}"),
    ("bell", "\u{e6a6}"),
    ("bar-chart-o", "\u{e6a7}"),
    ("balance-list-o", "\u{e6a8}"),
    ("bars", "\u{e6a9}"),
    ("balance-pay", "\u{e6aa}"),
    ("birthday-cake-o", "\u{e6ab}"),
    ("bill-o", "\u{e6ac}"),
    ("bookmark", "\u{e6ad}"),
    ("bookmark-o", "\u{e6ae}"),
    ("browsing-history-o", "\u{e6af}"),
    ("brush-o", "\u{e6b0}"),
    ("bill", "\u{e6b1}"),
    ("bullhorn-o", "\u{e6b2}"),
    ("bulb-o", "\u{e6b3}"),
    ("calendar-o", "\u{e6b4}"),
    ("card", "\u{e6b5}"),
    ("cart", "\u{e6b6}"),
    ("browsing-history", "\u{e6b7}"),
    ("cart-o", "\u{e6b8}"),
    ("chart-trending-o", "\u{e6b9}"),
    ("cash-back-record", "\u{e6ba}"),
    ("certificate", "\u{e6bb}"),
    ("cashier-o", "\u{e6bc}"),
    ("cash-on-deliver", "\u{e6bd}"),
    ("cart-circle-o", "\u{e6be}"),
    ("chat", "\u{e6bf}"),
    ("clock", "\u{e6c0}"),
    ("circle", "\u{e6c1}"),
    ("cart-circle", "\u{e6c2}"),
    ("apps-o", "\u{e6c3}"),
    ("clear", "\u{e6c4}"),
    ("close", "\u{e6c5}"),
    ("checked", "\u{e6c6}"),
    ("comment-o", "\u{e6c7}"),
    ("comment-circle-o", "\u{e6c8}"),
    ("closed-eye", "\u{e6c9}"),
    ("clock-o", "\u{e6ca}"),
    ("cluster-o", "\u{e6cb}"),
    ("chat-o", "\u{e6cc}"),
    ("cluster", "\u{e6cd}"),
    ("column", "\u{e6ce}"),
    ("comment", "\u{e6cf}"),
    ("contact", "\u{e6d0}"),
    ("credit-pay", "\u{e6d1}"),
    ("coupon-o", "\u{e6d2}"),
    ("completed", "\u{e6d3}"),
    ("descending", "\u{e6d4}"),
    ("comment-circle", "\u{e6d5}"),
    ("cross", "\u{e6d6}"),
    ("diamond", "\u{e6d7}"),
    ("delete", "\u{e6d8}"),
    ("diamond-o", "\u{e6d9}"),
    ("envelop-o", "\u{e6da}"),
    ("desktop-o", "\u{e6db}"),
    ("ecard-pay", "\u{e6dc}"),
    ("edit", "\u{e6dd}"),
    ("coupon", "\u{e6de}"),
    ("delete-o", "\u{e6df}"),
    ("down", "\u{e6e0}"),
    ("ellipsis", "\u{e6e1}"),
    ("enlarge", "\u{e6e2}"),
    ("expand", "\u{e6e3}"),
    ("description", "\u{e6e4}"),
    ("exchange", "\u{e6e5}"),
    ("eye-o", "\u{e6e6}"),
    ("expand-o", "\u{e6e7}"),
    ("eye", "\u{e6e8}"),
    ("fail", "\u{e6e9}"),
    ("debit-pay", "\u{e6ea}"),
    ("discount", "\u{e6eb}"),
    ("filter-o", "\u{e6ec}"),
    ("failure", "\u{e6ed}"),
    ("fire", "\u{e6ee}"),
    ("font", "\u{e6ef}"),
    ("flag-o", "\u{e6f0}"),
    ("font-o", "\u{e6f1}"),
    ("fire-o", "\u{e6f2}"),
    ("free-postage", "\u{e6f3}"),
    ("friends-o", "\u{e6f4}"),
    ("gem-o", "\u{e6f5}"),
    ("gem", "\u{e6f6}"),
    ("friends", "\u{e6f7}"),
    ("gift", "\u{e6f8}"),
    ("gift-o", "\u{e6f9}"),
    ("gift-card-o", "\u{e6fa}"),
    ("good-job", "\u{e6fb}"),
    ("flower-o", "\u{e6fc}"),
    ("gold-coin-o", "\u{e6fd}"),
    ("gold-coin", "\u{e6fe}"),
    ("good-job-o", "\u{e6ff}"),
    ("home-o", "\u{e700}"),
    ("gift-card", "\u{e701}"),
    ("graphic", "\u{e702}"),
    ("hot-o", "\u{e703}"),
    ("idcard", "\u{e704}"),
    ("hot-sale-o", "\u{e705}"),
    ("info", "\u{e706}"),
    ("hot-sale", "\u{e707}"),
    ("hotel-o", "\u{e708}"),
    ("goods-collect-o", "\u{e709}"),
    ("info-o", "\u{e70a}"),
    ("label-o", "\u{e70b}"),
    ("hot", "\u{e70c}"),
    ("label", "\u{e70d}"),
    ("location-o", "\u{e70e}"),
    ("goods-collect", "\u{e70f}"),
    ("location", "\u{e710}"),
    ("lock", "\u{e711}"),
    ("like-o", "\u{e712}"),
    ("live", "\u{e713}"),
    ("manager-o", "\u{e714}"),
    ("like", "\u{e715}"),
    ("manager", "\u{e716}"),
    ("map-marked", "\u{e717}"),
    ("logistics", "\u{e718}"),
    ("more", "\u{e719}"),
    ("medal", "\u{e71a}"),
    ("more-o", "\u{e71b}"),
    ("music-o", "\u{e71c}"),
    ("music", "\u{e71d}"),
    ("newspaper-o", "\u{e71e}"),
    ("minus", "\u{e71f}"),
    ("new-o", "\u{e720}"),
    ("new-arrival", "\u{e721}"),
    ("notes-o", "\u{e722}"),
    ("orders-o", "\u{e723}"),
    ("medal-o", "\u{e724}"),
    ("new", "\u{e725}"),
    ("new-arrival-o", "\u{e726}"),
    ("other-pay", "\u{e727}"),
    ("pause", "\u{e728}"),
    ("peer-pay", "\u{e729}"),
    ("paid", "\u{e72a}"),
    ("passed", "\u{e72b}"),
    ("pause-circle", "\u{e72c}"),
    ("pause-circle-o", "\u{e72d}"),
    ("phone-o", "\u{e72e}"),
    ("pending-payment", "\u{e72f}"),
    ("photograph", "\u{e730}"),
    ("phone-circle", "\u{e731}"),
    ("photo-fail", "\u{e732}"),
    ("phone", "\u{e733}"),
    ("photo", "\u{e734}"),
    ("plus", "\u{e735}"),
    ("photo-o", "\u{e736}"),
    ("play", "\u{e737}"),
    ("play-circle", "\u{e738}"),
    ("share", "\u{e739}"),
    ("point-gift", "\u{e73a}"),
    ("points", "\u{e73b}"),
    ("point-gift-o", "\u{e73c}"),
    ("printer", "\u{e73d}"),
    ("revoke", "\u{e73e}"),
    ("search", "\u{e73f}"),
    ("phone-circle-o", "\u{e740}"),
    ("send-gift-o", "\u{e741}"),
    ("scan", "\u{e742}"),
    ("send-gift", "\u{e743}"),
    ("qr", "\u{e744}"),
    ("play-circle-o", "\u{e745}"),
    ("stop-circle-o", "\u{e746}"),
    ("service-o", "\u{e747}"),
    ("qr-invalid", "\u{e748}"),
    ("question-o", "\u{e749}"),
    ("records", "\u{e74a}"),
    ("setting", "\u{e74b}"),
    ("setting-o", "\u{e74c}"),
    ("shop-collect-o", "\u{e74d}"),
    ("share-o", "\u{e74e}"),
    ("replay", "\u{e74f}"),
    ("question", "\u{e750}"),
    ("refund-o", "\u{e751}"),
    ("shopping-cart", "\u{e752}"),
    ("shop", "\u{e753}"),
    ("shopping-cart-o", "\u{e754}"),
    ("shop-collect", "\u{e755}"),
    ("sign", "\u{e756}"),
    ("shop-o", "\u{e757}"),
    ("smile-o", "\u{e758}"),
    ("smile-comment-o", "\u{e759}"),
    ("stop", "\u{e75a}"),
    ("smile", "\u{e75b}"),
    ("stop-circle", "\u{e75c}"),
    ("success", "\u{e75d}"),
    ("smile-comment", "\u{e75e}"),
    ("tosend", "\u{e75f}"),
    ("star-o", "\u{e760}"),
    ("todo-list", "\u{e761}"),
    ("sort", "\u{e762}"),
    ("underway", "\u{e763}"),
    ("tv-o", "\u{e764}"),
    ("umbrella-circle", "\u{e765}"),
    ("video", "\u{e766}"),
    ("vip-card", "\u{e767}"),
    ("service", "\u{e768}"),
    ("volume-o", "\u{e769}"),
    ("user-o", "\u{e76a}"),
    ("wap-nav", "\u{e76b}"),
    ("wap-home", "\u{e76c}"),
    ("video-o", "\u{e76d}"),
    ("wap-home-o", "\u{e76e}"),
    ("weapp-nav", "\u{e76f}"),
    ("underway-o", "\u{e770}"),
    ("warning", "\u{e771}"),
    ("vip-card-o", "\u{e772}"),
    ("volume", "\u{e773}"),
    ("user-circle-o", "\u{e774}"),
    ("warn-o", "\u{e775}")
])

/*
* 注册字体图标
* name: family
* path: ttf文件路径
* map: 映射文件（iconfont.css文件中（类名，content值））
*/
public func SilkIconRegisterIconFont (name: String, path: String, map: HashMap<String, String>) {
    Font.registerFont(familyName: name, familySrc: path)
    IconNames.putAll(map)
}

