/**
 * Created on 2025/7/9
 *
 * SilkTabs 标签页组件
 *
 * 标签页组件用于在多个内容面板之间进行切换，支持多种样式和交互方式。
 * 支持滑动切换、粘性定位、懒加载等功能。
 *
 * @module silkui/components/tabs
 */
package silkui.components.tabs
import silkui.macros.EnumExtend
import std.overflow.ThrowingOp
import ohos.base.*
import ohos.resource_manager.*
import silkui.ResourceColor
import silkui.ResourceStr
import silkui.SilkUIPaddingOptions
import silkui.constants.getTabsColorConstant
import silkui.constants.SilkTabsColorKey
import silkui.constants.getTabsSizeConstant
import silkui.constants.SilkTabsSizeKey
import silkui.constants.getColorConstant
import silkui.constants.SilkColorKey

/**
 * 标签页类型枚举
 *
 * 定义标签页的显示类型
 */
@EnumExtend
public enum SilkTabsType {
    | LINE     // 线条类型
    | CARD     // 卡片类型
}

/**
 * 标签页单个标签配置
 *
 * 定义单个标签的各种属性
 */
public struct SilkTabItem {
    /**
     * 标题
     */
    public let title: ResourceStr

    /**
     * 是否禁用标签
     * 默认为 false
     */
    public let disabled: Bool

    /**
     * 是否在标题右上角显示小红点
     * 默认为 false
     */
    public let dot: Bool

    /**
     * 图标右上角徽标的内容（dot 为 false 时生效）
     * 默认为空字符串
     */
    public let badge: ResourceStr

    /**
     * 标签名称，作为匹配的标识符
     * 默认为标签的索引值
     */
    public let name: ResourceStr

    /**
     * 点击后跳转的链接地址
     * 默认为空字符串
     */
    public let url: ResourceStr

    /**
     * 点击后跳转的目标路由对象
     * 默认为空字符串
     */
    public let to: ResourceStr

    /**
     * 是否在跳转时替换当前页面历史
     * 默认为 false
     */
    public let replace: Bool

    /**
     * 自定义标题样式
     * 默认为空字符串
     */
    public let titleStyle: ResourceStr

    /**
     * 自定义标题类名
     * 默认为空字符串
     */
    public let titleClass: ResourceStr

    /**
     * 当 badge 为数字 0 时，是否展示徽标
     * 默认为 true
     */
    public let showZeroBadge: Bool

    /**
     * 标签点击回调
     * 默认为 None
     */
    public let callback: Option<() -> Unit>

    /**
     * 创建标签页单个标签配置
     *
     * @param title 标题
     * @param disabled 是否禁用标签，默认为 false
     * @param dot 是否在标题右上角显示小红点，默认为 false
     * @param badge 图标右上角徽标的内容，默认为空字符串
     * @param name 标签名称，作为匹配的标识符，默认为空字符串
     * @param url 点击后跳转的链接地址，默认为空字符串
     * @param to 点击后跳转的目标路由对象，默认为空字符串
     * @param replace 是否在跳转时替换当前页面历史，默认为 false
     * @param titleStyle 自定义标题样式，默认为空字符串
     * @param titleClass 自定义标题类名，默认为空字符串
     * @param showZeroBadge 当 badge 为数字 0 时，是否展示徽标，默认为 true
     * @param callback 标签点击回调，默认为 None
     */
    public init (
        title!: ResourceStr,
        disabled!: Bool = false,
        dot!: Bool = false,
        badge!: ResourceStr = String.empty,
        name!: ResourceStr = String.empty,
        url!: ResourceStr = String.empty,
        to!: ResourceStr = String.empty,
        replace!: Bool = false,
        titleStyle!: ResourceStr = String.empty,
        titleClass!: ResourceStr = String.empty,
        showZeroBadge!: Bool = true,
        callback!: Option<() -> Unit> = Option.None
    ) {
        this.title = title
        this.disabled = disabled
        this.dot = dot
        this.badge = badge
        this.name = name
        this.url = url
        this.to = to
        this.replace = replace
        this.titleStyle = titleStyle
        this.titleClass = titleClass
        this.showZeroBadge = showZeroBadge
        this.callback = callback
    }
}

/**
 * 标签页配置选项
 *
 * 用于配置标签页的各种属性，包括类型、样式、交互等
 */
public struct SilkTabsOptions {
    /**
     * 标签页类型
     * 默认为 LINE
     */
    public let tabsType: SilkTabsType

    /**
     * 标签主题色
     * 默认使用预设颜色
     */
    public let color: ResourceColor

    /**
     * 是否显示标签栏外边框
     * 默认为 false
     */
    public let border: Bool

    /**
     * 是否使用粘性定位布局
     * 默认为 false
     */
    public let sticky: Bool

    /**
     * 是否开启左侧收缩布局
     * 默认为 false
     */
    public let shrink: Bool

    /**
     * 绑定当前选中标签的标识符
     * 默认为 0
     */
    public let active: Int32

    /**
     * 动画时间，单位毫秒
     * 默认为 300
     */
    public let duration: Int32

    /**
     * 是否开启切换标签内容时的转场动画
     * 默认为 false
     */
    public let animated: Bool

    /**
     * 是否省略过长的标题文字
     * 默认为 true
     */
    public let ellipsis: Bool

    /**
     * 是否开启手势滑动切换
     * 默认为 false
     */
    public let swipeable: Bool

    /**
     * 是否开启滚动导航
     * 默认为 false
     */
    public let scrollspy: Bool

    /**
     * 粘性定位布局下与顶部的最小距离，支持 vp 单位
     * 默认为 0
     */
    public let offsetTop: Length

    /**
     * 标签栏背景色
     * 默认使用预设颜色
     */
    public let background: ResourceColor

    /**
     * 是否显示标签栏
     * 默认为 true
     */
    public let showHeader: Bool

    /**
     * 底部条宽度，默认单位 vp
     * 默认使用预设宽度
     */
    public let lineWidth: Length

    /**
     * 底部条高度，默认单位 vp
     * 默认使用预设高度
     */
    public let lineHeight: Length

    /**
     * 切换标签前的回调函数，返回 false 可阻止切换，支持返回 Promise
     * 默认为 None
     */
    public let beforeChange: Option<(name: String) -> Future<Bool>>

    /**
     * 滚动阈值，标签数量超过阈值且总宽度超过标签栏宽度时开始横向滚动
     * 默认为 5
     */
    public let swipeThreshold: Int32

    /**
     * 标题选中态颜色
     * 默认使用预设颜色
     */
    public let titleActiveColor: ResourceColor

    /**
     * 标题默认态颜色
     * 默认使用预设颜色
     */
    public let titleInactiveColor: ResourceColor

    /**
     * 创建标签页配置选项
     *
     * @param tabsType 标签页类型，默认为 LINE
     * @param color 标签主题色，默认使用预设颜色
     * @param border 是否显示标签栏外边框，默认为 false
     * @param sticky 是否使用粘性定位布局，默认为 false
     * @param shrink 是否开启左侧收缩布局，默认为 false
     * @param active 绑定当前选中标签的标识符，默认为 0
     * @param duration 动画时间，单位毫秒，默认为 300
     * @param animated 是否开启切换标签内容时的转场动画，默认为 false
     * @param ellipsis 是否省略过长的标题文字，默认为 true
     * @param swipeable 是否开启手势滑动切换，默认为 false
     * @param scrollspy 是否开启滚动导航，默认为 false
     * @param offsetTop 粘性定位布局下与顶部的最小距离，默认为 0
     * @param background 标签栏背景色，默认使用预设颜色
     * @param lazyRender 是否开启延迟渲染，默认为 true
     * @param showHeader 是否显示标签栏，默认为 true
     * @param lineWidth 底部条宽度，默认使用预设宽度
     * @param lineHeight 底部条高度，默认使用预设高度
     * @param beforeChange 切换标签前的回调函数，默认为 None
     * @param swipeThreshold 滚动阈值，默认为 5
     * @param titleActiveColor 标题选中态颜色，默认使用预设颜色
     * @param titleInactiveColor 标题默认态颜色，默认使用预设颜色
     */
    public init (
        tabsType!: SilkTabsType = SilkTabsType.LINE,
        color!: ResourceColor = getTabsColorConstant(SilkTabsColorKey.TABS_DEFAULT_COLOR),
        border!: Bool = false,
        sticky!: Bool = false,
        shrink!: Bool = false,
        active!: Int32 = 0,
        duration!: Int32 = 300,
        animated!: Bool = false,
        ellipsis!: Bool = true,
        swipeable!: Bool = false,
        scrollspy!: Bool = false,
        offsetTop!: Length = 0.vp,
        background!: ResourceColor = getTabsColorConstant(SilkTabsColorKey.TABS_NAV_BACKGROUND),
        showHeader!: Bool = true,
        lineWidth!: Length = getTabsSizeConstant(SilkTabsSizeKey.TABS_BOTTOM_BAR_WIDTH),
        lineHeight!: Length = getTabsSizeConstant(SilkTabsSizeKey.TABS_BOTTOM_BAR_HEIGHT),
        beforeChange!: Option<(name: String) -> Future<Bool>> = Option.None,
        swipeThreshold!: Int32 = 5,
        titleActiveColor!: ResourceColor = getTabsColorConstant(SilkTabsColorKey.TAB_ACTIVE_TEXT_COLOR),
        titleInactiveColor!: ResourceColor = getTabsColorConstant(SilkTabsColorKey.TAB_TEXT_COLOR)
    ) {
        this.tabsType = tabsType
        this.color = color
        this.border = border
        this.sticky = sticky
        this.shrink = shrink
        this.active = active
        this.duration = duration
        this.animated = animated
        this.ellipsis = ellipsis
        this.swipeable = swipeable
        this.scrollspy = scrollspy
        this.offsetTop = offsetTop
        this.background = background
        this.showHeader = showHeader
        this.lineWidth = lineWidth
        this.lineHeight = lineHeight
        this.beforeChange = beforeChange
        this.swipeThreshold = swipeThreshold
        this.titleActiveColor = titleActiveColor
        this.titleInactiveColor = titleInactiveColor
    }
}