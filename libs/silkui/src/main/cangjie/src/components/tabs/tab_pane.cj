/**
 * Created on 2025/7/9
 *
 * SilkTabPane 标签页面板组件
 *
 * 标签页面板组件用于包装标签页的内容区域，支持懒加载等功能。
 *
 * @module silkui/components/tabs
 */
package silkui.components.tabs
import ohos.base.*
import ohos.component.*
import ohos.state_manage.*
import ohos.state_macro_manage.*
import silkui.ResourceStr
import silkui.utils.ResourceStrToString

/**
 * 标签页面板组件
 *
 * 用于包装标签页的内容区域
 */
@Component
public class SilkTabPane {
    /**
     * 面板标题
     */
    public var title: ResourceStr = String.empty

    /**
     * 面板名称，作为匹配的标识符
     */
    public var name: ResourceStr = String.empty

    /**
     * 是否禁用面板
     */
    public var disabled: Bool = false

    /**
     * 是否在标题右上角显示小红点
     */
    public var dot: Bool = false

    /**
     * 图标右上角徽标的内容
     */
    public var badge: ResourceStr = String.empty

    /**
     * 当前面板是否激活
     */
    @Prop
    @Watch[onActiveChange]
    var active: Bool

    /**
     * 是否启用懒加载
     */
    public var lazyRender: Bool = true

    /**
     * 面板内容构建器
     */
    @BuilderParam
    var Content: () -> Unit

    /**
     * 是否有自定义内容
     */
    var hasContent: Bool = false

    /**
     * 面板是否已经渲染过
     */
    @State
    var hasRendered: Bool = false

    /**
     * 组件初始化
     */
    protected override func aboutToAppear() {
        // 如果当前面板激活或不启用懒加载，则标记为已渲染
        if (active || !lazyRender) {
            hasRendered = true
        }
    }

    /**
     * 监听激活状态变化
     */
//    func watchActive() {}

    /**
     * 激活状态变化处理
     */
    func onActiveChange() {
        if (active && !hasRendered) {
            hasRendered = true
        }
    }

    /**
     * 构建组件UI
     */
    func build() {
        Column() {
            if (active) {
                if (hasContent && (hasRendered || !lazyRender)) {
                    Content()
                }
            }
        }
        .width(100.percent)
        .visibility(if (active) { Visibility.Visible } else { Visibility.Hidden })
    }
}
