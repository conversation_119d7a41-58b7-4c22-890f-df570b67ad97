/**
 * Created on 2025/7/9
 *
 * SilkTabs 标签页组件
 *
 * 标签页组件用于在多个内容面板之间进行切换，支持多种样式和交互方式。
 * 支持滑动切换、粘性定位、懒加载等功能。
 *
 * ## 基础用法
 * ```
 * SilkTabs(
 *   tabs: [
 *     SilkTabItem(title: "标签1"),
 *     SilkTabItem(title: "标签2"),
 *     SilkTabItem(title: "标签3")
 *   ],
 *   props: SilkTabsOptions()
 * )
 * ```
 *
 * ## 标签页类型
 * 支持 `line` 和 `card` 两种类型，默认为 `line`。
 * ```
 * SilkTabs(
 *   tabs: tabs,
 *   props: SilkTabsOptions(tabsType: SilkTabsType.CARD)
 * )
 * ```
 *
 * ## 自定义颜色
 * 通过 `color` 属性可以自定义标签页的主题色。
 * ```
 * SilkTabs(
 *   tabs: tabs,
 *   props: SilkTabsOptions(color: Color(255, 0, 0, alpha: 1.0))
 * )
 * ```
 *
 * @module silkui/components/tabs
 */
package silkui.components.tabs
import ohos.base.*
import ohos.component.*
import ohos.state_manage.*
import ohos.state_macro_manage.*
import std.collection.*
import silkui.ResourceColor
import silkui.ResourceStr
import silkui.utils.ResourceColorToColor
import silkui.utils.ResourceStrToString
import silkui.constants.getTabsColorConstant
import silkui.constants.SilkTabsColorKey
import silkui.constants.getTabsSizeConstant
import silkui.constants.SilkTabsSizeKey
import silkui.utils.SilkUILog

/**
 * 标签页组件
 *
 * 用于在多个内容面板之间进行切换
 */
@Component
public class SilkTabs {
    /**
     * 标签页列表
     */
    public var tabs: Array<SilkTabItem> = Array<SilkTabItem>()

    /**
     * 标签页配置选项
     */
    public var props: SilkTabsOptions = SilkTabsOptions()

    /**
     * 当前激活的标签索引
     */
    @Link
    @Watch[changeIndex]
    var currentActive: Int32

    private func changeIndex () {
        SilkUILog.info("===222===执行了${currentActive}")
        _Content = match (Content) {
        	case Some(content) => SilkUILog.info("===222===执行了====${currentActive}");content(currentActive)
        	case None => None
        	}
    }
    /**
     * 标签页内容构建器
     */
    public var Content: Option<(index: Int32) -> ((CustomView) -> ViewBuilder)> = Option.None

    private var _Content: Option<(CustomView) -> ViewBuilder> = Option.None

    /**
     * 是否有自定义内容
     */
    var hasContent: Bool = false

    /**
     * 标签页控制器
     */
    public var controller: Option<SilkTabsController> = Option.None

    /**
     * 标签宽度数组
     */
    @State
    @Watch[initActive]
    var tabItemWidth: Array<Float64> = Array<Float64>()

    /**
     * 标签左偏移数组
     */
    @State
    var tabItemLeft: Array<Float64> = Array<Float64>()

    /**
     * 指示器左偏移
     */
    @State
    var moveLeft: Float64 = 0.0

    /**
     * 指示器宽度
     */
    @State
    var moveWidth: Float64 = 0.0

    /**
     * 动画时长
     */
    @State
    var activeDuration: Int32 = 300

    /**
     * 滚动控制器
     */
    var scrollController: Scroller = Scroller()

    /**
     * 标签栏宽度
     */
    var barWidth: Float64 = 0.0

    /**
     * 内容区域宽度
     */
    var contentWidth: Float64 = 0.0

    /**
     * 标签点击回调
     */
    var onTabClick: Option<(tab: SilkTabItem, index: Int32) -> Unit> = Option.None

    /**
     * 标签切换回调
     */
    var onTabChange: Option<(name: String, index: Int32) -> Unit> = Option.None

    /**
     * 初始化激活状态
     */
    private func initActive(): Unit {
        if (tabItemWidth.size == 0) {
            return
        }

        // 检查所有宽度是否已设置
        var validCount: Int64 = 0
        for (width in tabItemWidth) {
            if (width > 0.0) {
                validCount += 1
            }
        }

        if (validCount >= tabs.size) {
            // 根据标签样式设置指示器位置
            match (props.tabsType) {
                case SilkTabsType.CARD =>
                    if (currentActive == 0) {
                        moveLeft = tabItemLeft[0]
                        moveWidth = tabItemWidth[0]
                    } else {
                        moveLeft = tabItemLeft[Int64(currentActive)]
                        moveWidth = tabItemWidth[Int64(currentActive)]
                    }
                case SilkTabsType.LINE =>
                    let lineWidth = 20.0 // 默认线条宽度
                    if (currentActive == 0) {
                        moveLeft = tabItemLeft[0] + tabItemWidth[0] / 2.0 - lineWidth / 2.0
                    } else {
                        moveLeft = tabItemLeft[Int64(currentActive)] + tabItemWidth[Int64(currentActive)] / 2.0 - lineWidth / 2.0
                    }
                    moveWidth = lineWidth
            }
        }
    }

    /**
     * 组件初始化
     */
    protected func aboutToAppear(){
        currentActive = props.active
        _Content = match (Content) {
        	case Some(content) => content(currentActive)
        	case None => None
        	}

        // 初始化标签宽度数组
        tabItemWidth = Array<Float64>(tabs.size, item: 0.0)
        tabItemLeft = Array<Float64>(tabs.size, item: 0.0)
    }

    /**
     * 处理标签点击事件
     */
    func handleTabClick(tab: SilkTabItem, index: Int32) {
        // 如果标签被禁用，不处理点击
        if (tab.disabled) {
            return
        }

        // 执行beforeChange回调
        if (props.beforeChange.isSome()) {
            let beforeChangeCallback = props.beforeChange.getOrThrow()
            let tabName = if (tab.name is String && (tab.name as String) == String.empty) { index.toString() } else { ResourceStrToString(tab.name) }
            // 这里应该处理异步回调，暂时简化处理
            // let result = beforeChangeCallback(tabName)
            // if (!result) return
        }

        // 更新当前激活标签
        currentActive = index

        // 执行标签点击回调
        if (onTabClick.isSome()) {
            onTabClick.getOrThrow()(tab, index)
        }

        // 执行标签切换回调
        if (onTabChange.isSome()) {
            let tabName = if (tab.name is String && (tab.name as String) == String.empty) { index.toString() } else { ResourceStrToString(tab.name) }
            onTabChange.getOrThrow()(tabName, index)
        }

        // 执行标签自身的回调
        if (tab.callback.isSome()) {
            tab.callback.getOrThrow()()
        }
    }

    /**
     * 获取标签文字颜色
     */
    func getTabTextColor(tab: SilkTabItem, index: Int32): ResourceColor {
        if (tab.disabled) {
            return getTabsColorConstant(SilkTabsColorKey.TAB_DISABLED_TEXT_COLOR)
        }
        if (index == currentActive) {
            return props.titleActiveColor
        }
        return props.titleInactiveColor
    }

    /**
     * 构建单个标签
     */
    @Builder
    func buildTabItem(tab: SilkTabItem, index: Int32) {
        Column() {
            Row() {
                Text(ResourceStrToString(tab.title))
                    .fontSize(getTabsSizeConstant(SilkTabsSizeKey.TAB_FONT_SIZE))
                    .lineHeight(getTabsSizeConstant(SilkTabsSizeKey.TAB_LINE_HEIGHT))
                    .fontColor(ResourceColorToColor(getTabTextColor(tab, index)))
                    .textAlign(TextAlign.Center)
                    .maxLines(if (props.ellipsis) { 1 } else { 999 })
                    .textOverflow(if (props.ellipsis) { TextOverflow.Ellipsis } else { TextOverflow.None })

                // 徽标或小红点
                if (tab.dot) {
                    Circle()
                        .width(8.vp)
                        .height(8.vp)
                        .fill(Color.RED)
                        .margin(left: 4.vp, top: Length(-2, unitType: LengthType.vp))
                } else if (!ResourceStrToString(tab.badge).isEmpty()) {
                    Text(ResourceStrToString(tab.badge))
                        .fontSize(10.vp)
                        .fontColor(Color.WHITE)
                        .backgroundColor(Color.RED)
                        .borderRadius(8.vp)
                        .padding(left: 4.vp, right: 4.vp, top: 1.vp, bottom: 1.vp)
                        .margin(left: 4.vp, top: Length(-2, unitType: LengthType.vp))
                        .visibility(if (tab.showZeroBadge || ResourceStrToString(tab.badge) != "0") {
                            Visibility.Visible
                        } else {
                            Visibility.Hidden
                        })
                }
            }
            .justifyContent(FlexAlign.Center)
            .alignItems(VerticalAlign.Center)

            // 底部指示条（仅在LINE类型且为激活状态时显示）
            if (props.tabsType == SilkTabsType.LINE && index == currentActive) {
                Line()
                    .width(props.lineWidth)
                    .height(props.lineHeight)
                    .fill(ResourceColorToColor(props.color))
                    .margin(top: 4.vp)
            }
        }
        .padding(left: 16.vp, right: 16.vp, top: 12.vp, bottom: 12.vp)
        .opacity(if (tab.disabled) { 0.5 } else { 1.0 })
        .onClick({ _ => handleTabClick(tab, index) })
    }

    /**
     * 构建标签栏
     */
    @Builder
    func buildTabBar() {
        if (props.showHeader) {
            Row() {
                ForEach(
                    tabs,
                    itemGeneratorFunc: { tab: SilkTabItem, index: Int64 =>
                        buildTabItem(tab, Int32(index))
                    }
                )
            }
            .width(100.percent)
            .height(getTabsSizeConstant(SilkTabsSizeKey.TABS_LINE_HEIGHT))
            .backgroundColor(ResourceColorToColor(props.background))
            .justifyContent(if (props.shrink) { FlexAlign.Start } else { FlexAlign.SpaceEvenly })
        }
    }

    /**
     * 构建组件UI
     */
    func build() {
        Column() {
            // 标签栏
            buildTabBar()

            // 内容区域
//            if (Content.isSome()) {
//                Content.getOrThrow()(currentActive)()
//            }
            if (_Content.isSome()) {
                _Content.getOrThrow()()
            }
        }
        .width(100.percent)
        .backgroundColor(ResourceColorToColor(props.background))
    }
}
