/**
 * Created on 2025/7/17
 *
 * SilkTabsController 标签页控制器
 *
 * 用于管理标签页的状态、动画和交互逻辑
 *
 * @module silkui/components/tabs
 */
package silkui.components.tabs

import ohos.base.*
import ohos.component.*
import ohos.state_manage.*
import ohos.state_macro_manage.*
import std.collection.*
import silkui.utils.SilkUILog

/**
 * 指示器数据接口
 */
public struct SilkIndicatorData {
    /**
     * 指示器左偏移量
     */
    public var left: Float64 = 0.0
    
    /**
     * 指示器宽度
     */
    public var width: Float64 = 0.0
    
    public init(left: Float64 = 0.0, width: Float64 = 0.0) {
        this.left = left
        this.width = width
    }
}

/**
 * 内容尺寸接口
 */
public struct ContentSize {
    /**
     * 宽度
     */
    public var width: Float64 = 0.0
    
    /**
     * 高度
     */
    public var height: Float64 = 0.0
    
    public init(width: Float64 = 0.0, height: Float64 = 0.0) {
        this.width = width
        this.height = height
    }
}

/**
 * 标签页控制器
 *
 * 管理标签页的状态、动画和交互逻辑
 * 提供统一的标签切换、指示器动画、滚动控制等功能
 */
public class SilkTabsController {
    /**
     * 当前激活的标签索引
     */
    private var currentIndex: Int32 = 0

    /**
     * 标签数量
     */
    private var tabCount: Int32 = 0

    /**
     * 指示器数据
     */
    private var indicator: SilkIndicatorData = SilkIndicatorData()

    /**
     * 标签宽度数组
     */
    private var tabWidths: Array<Float64> = Array<Float64>()

    /**
     * 标签左偏移数组
     */
    private var tabOffsets: Array<Float64> = Array<Float64>()

    /**
     * 容器尺寸
     */
    private var containerSize: ContentSize = ContentSize()

    /**
     * 标签栏尺寸
     */
    private var tabBarSize: ContentSize = ContentSize()

    /**
     * 标签样式
     */
    private var tabStyle: SilkTabsType = SilkTabsType.LINE

    /**
     * 动画配置
     */
    private var animationDuration: Int32 = 300
    private var isAnimating: Bool = false

    /**
     * 滚动配置
     */
    private var isScrollable: Bool = false
    private var scrollPosition: Float64 = 0.0

    /**
     * 构造函数
     */
    public init() {
        SilkUILog.info("TabsController initialized")
    }

    /**
     * 设置初始索引
     */
    public func setInitIndex(value: Int32): Unit {
        this.initIndex = value
    }

    /**
     * 设置标签数量
     */
    public func setTabItemLength(value: Int32): Unit {
        this.tabItemLength = value
    }

    /**
     * 设置指示器数据
     */
    public func setIndicator(value: SilkIndicatorData): Unit {
        this.indicator = value
    }

    /**
     * 设置组件尺寸
     */
    public func setTabsSize(value: ContentSize): Unit {
        this.contentSize = value
    }

    /**
     * 设置标签样式
     */
    public func setTabStyle(value: SilkTabsType): Unit {
        this.tabStyle = value
    }

    /**
     * 设置标签宽度数组
     */
    public func setTabItemWidth(value: Array<Float64>): Unit {
        this.tabItemWidth = value
    }

    /**
     * 设置标签左偏移数组
     */
    public func setTabItemLeft(value: Array<Float64>): Unit {
        this.tabItemLeft = value
    }

    /**
     * 设置标签栏尺寸
     */
    public func setBarSize(value: ContentSize): Unit {
        this.barSize = value
    }

    /**
     * 获取动画时长
     */
    public func getDuration(): Int32 {
        return this.duration
    }

    /**
     * 获取标签样式
     */
    public func getTabStyle(): SilkTabsType {
        return this.tabStyle
    }

    /**
     * 获取指示器数据
     */
    public func getIndicator(): SilkIndicatorData {
        return this.indicator
    }

    /**
     * 获取是否可滚动
     */
    public func getIsScroll(): Bool {
        return this.isScroll
    }

    /**
     * 初始化标签数据
     */
    public func initialize(tabCount: Int32, tabStyle: SilkTabsType, containerSize: ContentSize): Unit {
        this.tabCount = tabCount
        this.tabStyle = tabStyle
        this.containerSize = containerSize

        // 初始化数组
        this.tabWidths = Array<Float64>(Int64(tabCount), item: 0.0)
        this.tabOffsets = Array<Float64>(Int64(tabCount), item: 0.0)

        // 设置默认指示器宽度
        match (tabStyle) {
            case SilkTabsType.LINE => this.indicator.width = 20.0 // 默认线条宽度
            case SilkTabsType.CARD => this.indicator.width = 0.0 // 卡片模式宽度动态计算
        }

        SilkUILog.info("TabsController initialized with ${tabCount} tabs")
    }

    /**
     * 更新标签尺寸信息
     */
    public func updateTabSize(index: Int32, width: Float64, offset: Float64): Unit {
        if (index >= 0 && index < this.tabCount) {
            this.tabWidths[Int64(index)] = width
            this.tabOffsets[Int64(index)] = offset

            // 检查是否所有标签都已测量完成
            this.checkAndInitializeIndicator()
        }
    }

    /**
     * 检查并初始化指示器
     */
    private func checkAndInitializeIndicator(): Unit {
        // 检查所有标签是否都已测量
        var allMeasured = true
        for (width in this.tabWidths) {
            if (width <= 0.0) {
                allMeasured = false
                break
            }
        }

        if (allMeasured) {
            this.calculateScrollability()
            this.updateIndicatorPosition(this.currentIndex, false)
        }
    }

    /**
     * 计算是否需要滚动
     */
    private func calculateScrollability(): Unit {
        var totalWidth: Float64 = 0.0
        for (width in this.tabWidths) {
            totalWidth += width
        }
        this.isScrollable = totalWidth > this.tabBarSize.width
    }

    /**
     * 更新指示器位置
     */
    public func updateIndicatorPosition(index: Int32, animated: Bool): Unit {
        if (index < 0 || index >= this.tabCount) {
            return
        }

        this.currentIndex = index
        this.isAnimating = animated

        let tabIndex = Int64(index)
        let tabOffset = this.tabOffsets[tabIndex]
        let tabWidth = this.tabWidths[tabIndex]

        match (this.tabStyle) {
            case SilkTabsType.CARD =>
                this.indicator.left = tabOffset
                this.indicator.width = tabWidth
            case SilkTabsType.LINE =>
                this.indicator.left = tabOffset + tabWidth / 2.0 - this.indicator.width / 2.0
        }
    }

    /**
     * 切换到指定标签
     */
    public func switchToTab(index: Int32): Unit {
        if (index == this.currentIndex || index < 0 || index >= this.tabCount) {
            return
        }

        this.updateIndicatorPosition(index, true)
        this.updateScrollPosition(index)
    }

    /**
     * 更新滚动位置
     */
    private func updateScrollPosition(index: Int32): Unit {
        if (!this.isScrollable) {
            return
        }

        let tabIndex = Int64(index)
        let tabOffset = this.tabOffsets[tabIndex]
        let tabWidth = this.tabWidths[tabIndex]

        // 计算目标滚动位置，使标签居中显示
        let targetPosition = tabOffset + tabWidth / 2.0 - this.tabBarSize.width / 2.0
        this.scrollPosition = targetPosition
    }

    /**
     * 获取当前索引
     */
    public func getCurrentIndex(): Int32 {
        return this.currentIndex
    }

    /**
     * 获取指示器数据
     */
    public func getIndicatorData(): SilkIndicatorData {
        return this.indicator
    }

    /**
     * 获取动画时长
     */
    public func getAnimationDuration(): Int32 {
        return this.animationDuration
    }

    /**
     * 获取是否可滚动
     */
    public func getIsScrollable(): Bool {
        return this.isScrollable
    }

    /**
     * 获取滚动位置
     */
    public func getScrollPosition(): Float64 {
        return this.scrollPosition
    }

    /**
     * 设置标签栏尺寸
     */
    public func setTabBarSize(size: ContentSize): Unit {
        this.tabBarSize = size
        this.calculateScrollability()
    }

    /**
     * 重置控制器状态
     */
    public func reset(): Unit {
        this.currentIndex = 0
        this.isAnimating = false
        this.scrollPosition = 0.0
        this.indicator = SilkIndicatorData(0.0, 0.0)
    }
}
