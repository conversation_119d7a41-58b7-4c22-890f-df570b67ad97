/**
 * Created on 2025/7/17
 *
 * SilkTabsController 标签页控制器
 *
 * 用于管理标签页的状态、动画和交互逻辑
 *
 * @module silkui/components/tabs
 */
package silkui.components.tabs

import ohos.base.*
import ohos.component.*
import ohos.state_manage.*
import ohos.state_macro_manage.*
import std.collection.*
import silkui.utils.SilkUILog

/**
 * 指示器数据接口
 */
public struct SilkIndicatorData {
    /**
     * 指示器左偏移量
     */
    public var left: Float64 = 0.0
    
    /**
     * 指示器宽度
     */
    public var width: Float64 = 0.0
    
    public init(left: Float64 = 0.0, width: Float64 = 0.0) {
        this.left = left
        this.width = width
    }
}

/**
 * 内容尺寸接口
 */
public struct ContentSize {
    /**
     * 宽度
     */
    public var width: Float64 = 0.0
    
    /**
     * 高度
     */
    public var height: Float64 = 0.0
    
    public init(width: Float64 = 0.0, height: Float64 = 0.0) {
        this.width = width
        this.height = height
    }
}

/**
 * 标签页控制器
 *
 * 管理标签页的状态、动画和交互逻辑
 */
public class SilkTabsController {
    /**
     * 页签初始位置
     */
    private var initIndex: Int32 = 0
    
    /**
     * 页签数量
     */
    private var tabItemLength: Int32 = 0
    
    /**
     * 指示器数据
     */
    private var indicator: SilkIndicatorData = SilkIndicatorData()
    
    /**
     * 各元素宽度数组
     */
    private var tabItemWidth: Array<Float64> = Array<Float64>()
    
    /**
     * 各元素距离父级左边距
     */
    private var tabItemLeft: Array<Float64> = Array<Float64>()
    
    /**
     * tabs组件尺寸
     */
    private var contentSize: ContentSize = ContentSize()
    
    /**
     * 标签样式
     */
    private var tabStyle: SilkTabsType = SilkTabsType.LINE
    
    /**
     * 动画时长，单位毫秒
     */
    private var duration: Int32 = 300
    
    /**
     * 标签栏尺寸
     */
    private var barSize: ContentSize = ContentSize()
    
    /**
     * 是否可滚动
     */
    private var isScroll: Bool = false
    
    /**
     * 是否平均分布
     */
    private var spaceAround: Bool = true

    /**
     * 构造函数
     */
    public init() {
        // 初始化
    }

    /**
     * 设置初始索引
     */
    public func setInitIndex(value: Int32): Unit {
        this.initIndex = value
    }

    /**
     * 设置标签数量
     */
    public func setTabItemLength(value: Int32): Unit {
        this.tabItemLength = value
    }

    /**
     * 设置指示器数据
     */
    public func setIndicator(value: SilkIndicatorData): Unit {
        this.indicator = value
    }

    /**
     * 设置组件尺寸
     */
    public func setTabsSize(value: ContentSize): Unit {
        this.contentSize = value
    }

    /**
     * 设置标签样式
     */
    public func setTabStyle(value: SilkTabsType): Unit {
        this.tabStyle = value
    }

    /**
     * 设置标签宽度数组
     */
    public func setTabItemWidth(value: Array<Float64>): Unit {
        this.tabItemWidth = value
    }

    /**
     * 设置标签左偏移数组
     */
    public func setTabItemLeft(value: Array<Float64>): Unit {
        this.tabItemLeft = value
    }

    /**
     * 设置标签栏尺寸
     */
    public func setBarSize(value: ContentSize): Unit {
        this.barSize = value
    }

    /**
     * 获取动画时长
     */
    public func getDuration(): Int32 {
        return this.duration
    }

    /**
     * 获取标签样式
     */
    public func getTabStyle(): SilkTabsType {
        return this.tabStyle
    }

    /**
     * 获取指示器数据
     */
    public func getIndicator(): SilkIndicatorData {
        return this.indicator
    }

    /**
     * 获取是否可滚动
     */
    public func getIsScroll(): Bool {
        return this.isScroll
    }

    /**
     * 初始化激活状态
     */
    public func initActive(): Unit {
        if (this.tabItemWidth.size == 0) {
            return
        }
        
        // 检查所有宽度是否已设置
        var validCount: Int64 = 0
        for (width in this.tabItemWidth) {
            if (width > 0.0) {
                validCount += 1
            }
        }
        
        if (validCount >= this.tabItemLength) {
            // 计算总宽度判断是否需要滚动
            var totalWidth: Float64 = 0.0
            for (width in this.tabItemWidth) {
                totalWidth += width
            }
            this.isScroll = totalWidth > this.barSize.width
            
            // 根据标签样式设置指示器位置
            if (this.tabStyle == SilkTabsType.CARD) {
                if (this.initIndex == 0) {
                    this.indicator.left = this.tabItemLeft[0]
                    this.indicator.width = this.tabItemWidth[0]
                } else {
                    this.indicator.left = this.tabItemLeft[this.initIndex]
                    this.indicator.width = this.tabItemWidth[this.initIndex]
                }
            } else if (this.tabStyle == SilkTabsType.LINE) {
                if (this.initIndex == 0) {
                    this.indicator.left = this.tabItemLeft[0] + this.tabItemWidth[0] / 2.0 - this.indicator.width / 2.0
                } else {
                    this.indicator.left = this.tabItemLeft[this.initIndex] + this.tabItemWidth[this.initIndex] / 2.0 - this.indicator.width / 2.0
                }
            }
            
            SilkUILog.info("TabsController initActive: left=${this.indicator.left}, width=${this.indicator.width}")
        }
    }

    /**
     * 标签切换动画
     */
    public func tabActiveAnimate(index: Int32, target: Int32): Unit {
        this.duration = 300
        let leftW = this.tabItemLeft[target]
        
        var offsetX: Float64 = 0.0
        if (this.tabStyle == SilkTabsType.LINE) {
            offsetX = leftW + (this.tabItemWidth[target] - this.indicator.width) / 2.0
        } else {
            offsetX = leftW
        }
        
        var activeWidth = this.indicator.width
        if (this.tabStyle == SilkTabsType.CARD) {
            activeWidth = this.tabItemWidth[target]
        }
        
        this.indicator.width = activeWidth
        this.indicator.left = offsetX
        
        SilkUILog.info("TabsController tabActiveAnimate: target=${target}, left=${this.indicator.left}")
    }

    /**
     * 索引变化处理
     */
    public func changeIndex(currentIndex: Int32): Unit {
        this.duration = 300
        this.initIndex = currentIndex
        
        if (currentIndex == 0) {
            if (this.tabStyle == SilkTabsType.CARD) {
                this.indicator.left = this.tabItemLeft[0]
                this.indicator.width = this.tabItemWidth[0]
            } else {
                this.indicator.left = this.tabItemLeft[0] + this.tabItemWidth[0] / 2.0 - this.indicator.width / 2.0
            }
        } else if (currentIndex == this.tabItemLength - 1) {
            if (this.tabStyle == SilkTabsType.CARD) {
                this.indicator.left = this.tabItemLeft[currentIndex]
                this.indicator.width = this.tabItemWidth[this.tabItemLength - 1]
            } else {
                this.indicator.left = this.tabItemLeft[currentIndex] + this.tabItemWidth[currentIndex] / 2.0 - this.indicator.width / 2.0
            }
        } else {
            let value = this.tabItemLeft[currentIndex]
            if (this.tabStyle == SilkTabsType.CARD) {
                this.indicator.left = value
                this.indicator.width = this.tabItemWidth[currentIndex]
            } else {
                this.indicator.left = value + this.tabItemWidth[currentIndex] / 2.0 - this.indicator.width / 2.0
            }
        }
        
        SilkUILog.info("TabsController changeIndex: index=${currentIndex}, left=${this.indicator.left}")
    }
}
