# Tabs 标签页

### 介绍

标签页组件用于在多个内容面板之间进行切换，支持多种样式和交互方式。

### 引入

```js
import silkui.components.tabs.{SilkTabs, SilkTabPane, SilkTabItem, SilkTabsOptions, SilkTabsType}
```

## 代码演示

### 基础用法

通过 `SilkTabs` 组件和 `SilkTabItem` 配置标签页。

```js
@State
var activeTab: Int32 = 0

let tabs = [
    SilkTabItem(title: "标签1"),
    SilkTabItem(title: "标签2"),
    SilkTabItem(title: "标签3")
]

@Builder
func TabContent(index: Int32) {
    match (index) {
        case 0 => Text("标签1的内容").padding(20.vp)
        case 1 => Text("标签2的内容").padding(20.vp)
        case 2 => Text("标签3的内容").padding(20.vp)
        case _ => Text("默认内容").padding(20.vp)
    }
}

SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(active: activeTab),
    Content: TabContent,
    hasContent: true,
    onTabChange: { name, index => activeTab = index }
)
```

### 标签页类型

支持 `line` 和 `card` 两种类型，默认为 `line`。

```js
// 线条类型（默认）
SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(tabsType: SilkTabsType.LINE)
)

// 卡片类型
SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(tabsType: SilkTabsType.CARD)
)
```

### 禁用标签

通过 `disabled` 属性禁用标签。

```js
let tabs = [
    SilkTabItem(title: "标签1"),
    SilkTabItem(title: "标签2", disabled: true),
    SilkTabItem(title: "标签3")
]
```

### 徽标提示

支持在标签右上角显示小红点或徽标数字。

```js
let tabs = [
    SilkTabItem(title: "标签1", dot: true),
    SilkTabItem(title: "标签2", badge: "5"),
    SilkTabItem(title: "标签3", badge: "99+")
]
```

### 自定义颜色

通过 `color` 属性可以自定义标签页的主题色。

```js
SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(
        color: Color.blue,
        titleActiveColor: Color.blue,
        titleInactiveColor: Color.gray
    )
)
```

### 滚动导航

标签数量较多时，可以横向滚动切换。

```js
SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(
        shrink: true,
        swipeThreshold: 4
    )
)
```

### 切换动画

通过 `animated` 属性开启切换动画。

```js
SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(
        animated: true,
        duration: 300
    )
)
```

### 粘性定位

通过 `sticky` 属性可以将标签栏固定在顶部。

```js
SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(
        sticky: true,
        offsetTop: 50.vp
    )
)
```

### 切换前回调

通过 `beforeChange` 属性可以在切换前执行回调函数。

```js
SilkTabs(
    tabs: tabs,
    props: SilkTabsOptions(
        beforeChange: { name =>
            spawn {
                =>
                // 执行异步操作
                sleep(1 * Duration.second)
                true // 返回 true 允许切换，false 阻止切换
            }
        }
    )
)
```

## API

### SilkTabs Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| tabs | 标签页列表 | Array<SilkTabItem> | [] |
| props | 标签页配置选项 | SilkTabsOptions | SilkTabsOptions() |
| Content | 标签页内容构建器 | (index: Int32) -> Unit | - |
| hasContent | 是否有自定义内容 | Bool | false |
| onTabClick | 标签点击回调 | (tab: SilkTabItem, index: Int32) -> Unit | - |
| onTabChange | 标签切换回调 | (name: String, index: Int32) -> Unit | - |

### SilkTabsOptions Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| tabsType | 标签页类型 | SilkTabsType | LINE |
| color | 标签主题色 | ResourceColor | - |
| border | 是否显示标签栏外边框 | Bool | false |
| sticky | 是否使用粘性定位布局 | Bool | false |
| shrink | 是否开启左侧收缩布局 | Bool | false |
| active | 绑定当前选中标签的标识符 | Int32 | 0 |
| duration | 动画时间，单位毫秒 | Int32 | 300 |
| animated | 是否开启切换标签内容时的转场动画 | Bool | false |
| ellipsis | 是否省略过长的标题文字 | Bool | true |
| swipeable | 是否开启手势滑动切换 | Bool | false |
| scrollspy | 是否开启滚动导航 | Bool | false |
| offsetTop | 粘性定位布局下与顶部的最小距离 | Length | 0.vp |
| background | 标签栏背景色 | ResourceColor | - |
| showHeader | 是否显示标签栏 | Bool | true |
| lineWidth | 底部条宽度 | Length | 40.vp |
| lineHeight | 底部条高度 | Length | 3.vp |
| beforeChange | 切换标签前的回调函数 | (name: String) -> Future<Bool> | - |
| swipeThreshold | 滚动阈值 | Int32 | 5 |
| titleActiveColor | 标题选中态颜色 | ResourceColor | - |
| titleInactiveColor | 标题默认态颜色 | ResourceColor | - |

### SilkTabItem Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| title | 标题 | ResourceStr | - |
| disabled | 是否禁用标签 | Bool | false |
| dot | 是否在标题右上角显示小红点 | Bool | false |
| badge | 图标右上角徽标的内容 | ResourceStr | "" |
| name | 标签名称，作为匹配的标识符 | ResourceStr | "" |
| url | 点击后跳转的链接地址 | ResourceStr | "" |
| to | 点击后跳转的目标路由对象 | ResourceStr | "" |
| replace | 是否在跳转时替换当前页面历史 | Bool | false |
| titleStyle | 自定义标题样式 | ResourceStr | "" |
| titleClass | 自定义标题类名 | ResourceStr | "" |
| showZeroBadge | 当 badge 为数字 0 时，是否展示徽标 | Bool | true |
| callback | 标签点击回调 | () -> Unit | - |

### SilkTabsType

| 名称 | 说明 |
|------|------|
| LINE | 线条类型 |
| CARD | 卡片类型 |
