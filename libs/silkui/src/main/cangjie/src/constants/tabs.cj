/**
 * Created on 2025/7/9
 *
 * SilkTabs 标签页组件常量定义
 *
 * 本文件定义了标签页组件使用的各种常量，包括颜色、尺寸、字体等
 * 这里定义样式变量。由于泛型的问题，所以此处不使用 resource，全部采用 string
 *
 * @module silkui/constants/tabs
 */
package silkui.constants
import std.collection.HashMap
import ohos.base.Length
import ohos.base.LengthType
import ohos.base.Color
import ohos.state_macro_manage.*
import ohos.resource_manager.*
import cj_res_silkui.*
import silkui.ResourceColor
import silkui.ResourceStr
import silkui.SilkUIPaddingOptions
import ohos.component.FontWeight

/**
 * 标签页颜色常量枚举
 */
public enum SilkTabsColorKey {
    | TAB_TEXT_COLOR
    | TAB_ACTIVE_TEXT_COLOR
    | TAB_DISABLED_TEXT_COLOR
    | TABS_DEFAULT_COLOR
    | TABS_NAV_BACKGROUND
    | TABS_BOTTOM_BAR_COLOR
}

/**
 * 将标签页颜色枚举转换为字符串
 * @param key 标签页颜色枚举值
 * @return 对应的字符串键
 */
private func tabsColorKeyToString(key: SilkTabsColorKey): String {
    match (key) {
        case SilkTabsColorKey.TAB_TEXT_COLOR => "--silk-tab-text-color"
        case SilkTabsColorKey.TAB_ACTIVE_TEXT_COLOR => "--silk-tab-active-text-color"
        case SilkTabsColorKey.TAB_DISABLED_TEXT_COLOR => "--silk-tab-disabled-text-color"
        case SilkTabsColorKey.TABS_DEFAULT_COLOR => "--silk-tabs-default-color"
        case SilkTabsColorKey.TABS_NAV_BACKGROUND => "--silk-tabs-nav-background"
        case SilkTabsColorKey.TABS_BOTTOM_BAR_COLOR => "--silk-tabs-bottom-bar-color"
    }
}

/**
 * 标签页尺寸常量枚举
 */
public enum SilkTabsSizeKey {
    | TAB_FONT_SIZE
    | TAB_LINE_HEIGHT
    | TABS_LINE_HEIGHT
    | TABS_CARD_HEIGHT
    | TABS_BOTTOM_BAR_WIDTH
    | TABS_BOTTOM_BAR_HEIGHT
}

/**
 * 将标签页尺寸枚举转换为字符串
 * @param key 标签页尺寸枚举值
 * @return 对应的字符串键
 */
private func tabsSizeKeyToString(key: SilkTabsSizeKey): String {
    match (key) {
        case SilkTabsSizeKey.TAB_FONT_SIZE => "--silk-tab-font-size"
        case SilkTabsSizeKey.TAB_LINE_HEIGHT => "--silk-tab-line-height"
        case SilkTabsSizeKey.TABS_LINE_HEIGHT => "--silk-tabs-line-height"
        case SilkTabsSizeKey.TABS_CARD_HEIGHT => "--silk-tabs-card-height"
        case SilkTabsSizeKey.TABS_BOTTOM_BAR_WIDTH => "--silk-tabs-bottom-bar-width"
        case SilkTabsSizeKey.TABS_BOTTOM_BAR_HEIGHT => "--silk-tabs-bottom-bar-height"
    }
}

/**
 * 默认标签页颜色常量
 *
 * 定义了标签页的默认颜色常量，用于重置
 */
private let DefaultSilkTabsColorConstants: HashMap<String, ResourceColor> = HashMap<String, ResourceColor>([
    ("--silk-tab-text-color", getColorConstant(SilkColorKey.GRAY_7)),
    ("--silk-tab-active-text-color", getColorConstant(SilkColorKey.TEXT_COLOR)),
    ("--silk-tab-disabled-text-color", getColorConstant(SilkColorKey.TEXT_COLOR_3)),
    ("--silk-tabs-default-color", getColorConstant(SilkColorKey.PRIMARY_COLOR)),
    ("--silk-tabs-nav-background", getColorConstant(SilkColorKey.BACKGROUND_2)),
    ("--silk-tabs-bottom-bar-color", getColorConstant(SilkColorKey.PRIMARY_COLOR))
])

/**
 * 标签页颜色常量
 *
 * 定义了标签页的各种颜色常量，包括文字颜色、背景颜色等
 */
private let SilkTabsColorConstants: HashMap<String, ResourceColor> = HashMap<String, ResourceColor>([
    ("--silk-tab-text-color", getColorConstant(SilkColorKey.GRAY_7)),
    ("--silk-tab-active-text-color", getColorConstant(SilkColorKey.TEXT_COLOR)),
    ("--silk-tab-disabled-text-color", getColorConstant(SilkColorKey.TEXT_COLOR_3)),
    ("--silk-tabs-default-color", getColorConstant(SilkColorKey.PRIMARY_COLOR)),
    ("--silk-tabs-nav-background", getColorConstant(SilkColorKey.BACKGROUND_2)),
    ("--silk-tabs-bottom-bar-color", getColorConstant(SilkColorKey.PRIMARY_COLOR))
])

/**
 * 默认标签页尺寸常量
 *
 * 定义了标签页的默认尺寸常量，用于重置
 */
private let DefaultSilkTabsSizeConstants: HashMap<String, Length> = HashMap<String, Length>([
    ("--silk-tab-font-size", getSizeConstant(SilkSizeKey.FONT_SIZE_MD)),
    ("--silk-tab-line-height", getSizeConstant(SilkSizeKey.LINE_HEIGHT_MD)),
    ("--silk-tabs-line-height", Length(44, unitType: LengthType.vp)),
    ("--silk-tabs-card-height", Length(30, unitType: LengthType.vp)),
    ("--silk-tabs-bottom-bar-width", Length(40, unitType: LengthType.vp)),
    ("--silk-tabs-bottom-bar-height", Length(3, unitType: LengthType.vp))
])

/**
 * 标签页尺寸常量
 *
 * 定义了标签页的各种尺寸常量，包括大小、内边距、字体大小等
 */
private let SilkTabsSizeConstants: HashMap<String, Length> = HashMap<String, Length>([
    ("--silk-tab-font-size", getSizeConstant(SilkSizeKey.FONT_SIZE_MD)),
    ("--silk-tab-line-height", getSizeConstant(SilkSizeKey.LINE_HEIGHT_MD)),
    ("--silk-tabs-line-height", Length(44, unitType: LengthType.vp)),
    ("--silk-tabs-card-height", Length(30, unitType: LengthType.vp)),
    ("--silk-tabs-bottom-bar-width", Length(40, unitType: LengthType.vp)),
    ("--silk-tabs-bottom-bar-height", Length(3, unitType: LengthType.vp))
])

/**
 * 通过枚举获取标签页颜色常量的值
 * @param key 标签页颜色枚举键
 * @return 对应的颜色值
 */
public func getTabsColorConstant(key: SilkTabsColorKey): ResourceColor {
    let stringKey = tabsColorKeyToString(key)
    return SilkTabsColorConstants.get(stringKey).getOrThrow()
}

/**
 * 通过枚举修改标签页颜色常量的值
 * @param key 要修改的常量的枚举键
 * @param value 新的颜色值
 * @return 是否修改成功
 */
public func updateTabsColorConstant(key: SilkTabsColorKey, value: ResourceColor): Bool {
    let stringKey = tabsColorKeyToString(key)
    if (SilkTabsColorConstants.contains(stringKey)) {
        SilkTabsColorConstants.put(stringKey, value)
        return true
    }
    return false
}

/**
 * 通过枚举获取标签页尺寸常量的值
 * @param key 标签页尺寸枚举键
 * @return 对应的尺寸值
 */
public func getTabsSizeConstant(key: SilkTabsSizeKey): Length {
    let stringKey = tabsSizeKeyToString(key)
    return SilkTabsSizeConstants.get(stringKey).getOrThrow()
}

/**
 * 通过枚举修改标签页尺寸常量的值
 * @param key 要修改的常量的枚举键
 * @param value 新的尺寸值
 * @return 是否修改成功
 */
public func updateTabsSizeConstant(key: SilkTabsSizeKey, value: Length): Bool {
    let stringKey = tabsSizeKeyToString(key)
    if (SilkTabsSizeConstants.contains(stringKey)) {
        SilkTabsSizeConstants.put(stringKey, value)
        return true
    }
    return false
}

/**
 * 重置所有标签页常量为默认值
 *
 * 将所有标签页常量重置为初始默认值
 */
public func resetTabsConstants() {
    // 重置标签页颜色常量
    resetTabsColorConstants()

    // 重置标签页尺寸常量
    resetTabsSizeConstants()
}

/**
 * 重置标签页颜色常量为默认值
 */
public func resetTabsColorConstants() {
    SilkTabsColorConstants.clear()
    for ((key, value) in DefaultSilkTabsColorConstants) {
        SilkTabsColorConstants.put(key, value)
    }
}

/**
 * 重置标签页尺寸常量为默认值
 */
public func resetTabsSizeConstants() {
    SilkTabsSizeConstants.clear()
    for ((key, value) in DefaultSilkTabsSizeConstants) {
        SilkTabsSizeConstants.put(key, value)
    }
}
