[target]
  [target.aarch64-linux-ohos]
    compile-option = "-B \"${DEVECO_CANGJIE_HOME}/compiler/third_party/llvm/bin\" -B \"${DEVECO_CANGJIE_HOME}/musl/usr/lib/aarch64-linux-ohos\" -L \"${DEVECO_CANGJIE_HOME}/musl/usr/lib/aarch64-linux-ohos\" -L \"${DEVECO_CANGJIE_HOME}/build/linux_ohos_aarch64_llvm/openssl\" --sysroot \"${DEVECO_CANGJIE_HOME}/musl\""
    [target.aarch64-linux-ohos.bin-dependencies]
      path-option = [ "${AARCH64_LIBS}", "${AARCH64_MACRO_LIBS}", "${AARCH64_KIT_LIBS}" ]
      [target.aarch64-linux-ohos.bin-dependencies.package-option]
  [target.x86_64-linux-ohos]
    compile-option = "-B \"${DEVECO_CANGJIE_HOME}/compiler/third_party/llvm/bin\" -B \"${DEVECO_CANGJIE_HOME}/musl/usr/lib/x86_64-linux-ohos\" -L \"${DEVECO_CANGJIE_HOME}/musl/usr/lib/x86_64-linux-ohos\" -L \"${DEVECO_CANGJIE_HOME}/build/linux_ohos_x86_64_llvm/openssl\" --sysroot \"${DEVECO_CANGJIE_HOME}/musl\""
    [target.x86_64-linux-ohos.bin-dependencies]
      path-option = [ "${X86_64_OHOS_LIBS}", "${X86_64_OHOS_MACRO_LIBS}", "${X86_64_OHOS_KIT_LIBS}" ]

[dependencies]
  [dependencies.cj_res_entry]
    path = "./cj_res"
    version = "1.0.0"
  [dependencies.silkui]
    path = "../../../../libs/silkui/src/main/cangjie"

[package]
  cjc-version = "0.53.19"
  compile-option = ""
  description = "CangjieUI Application"
  link-option = ""
  name = "ohos_app_cangjie_entry"
  output-type = "dynamic"
  src-dir = "src"
  target-dir = ""
  version = "1.0.0"
  [package.package-configuration]
  [package.scripts]

[profile]
  [profile.build]
    incremental = true
    lto = ""
  [profile.customized-option]
    debug = "-g -Woff all"
    release = "--fast-math -O2 -s -Woff all"
  [profile.test]
