/**
 * Created on 2025/7/9
 */
package ohos_app_cangjie_entry.pages

internal import ohos.base.*
internal import ohos.component.*
internal import ohos.state_manage.*
import ohos.resource_manager.*
import ohos.state_macro_manage.*
import cj_res_entry.*
import ohos_app_cangjie_entry.components.{Nav, Title}
import silkui.components.tabs.{SilkTabs, SilkTabItem, SilkTabsOptions, SilkTabsType}

@Entry
@Component
class TabsPage {
    @State
    var activeTab1: Int32 = 0
    @State
    var activeTab2: Int32 = 0
    @State
    var activeTab3: Int32 = 0
    @State
    var activeTab4: Int32 = 0

    // 基础标签页数据
    let basicTabs = [
        SilkTabItem(title: "标签1"),
        SilkTabItem(title: "标签2"),
        SilkTabItem(title: "标签3"),
        SilkTabItem(title: "标签4")
    ]

    // 带徽标的标签页数据
    let badgeTabs = [
        SilkTabItem(title: "标签1", dot: true),
        SilkTabItem(title: "标签2", badge: "5"),
        SilkTabItem(title: "标签3", badge: "99+"),
        SilkTabItem(title: "标签4")
    ]

    // 禁用状态的标签页数据
    let disabledTabs = [
        SilkTabItem(title: "标签1"),
        SilkTabItem(title: "标签2", disabled: true),
        SilkTabItem(title: "标签3"),
        SilkTabItem(title: "标签4", disabled: true)
    ]

    // 多标签页数据
    let manyTabs = [
        SilkTabItem(title: "标签1"),
        SilkTabItem(title: "标签2"),
        SilkTabItem(title: "标签3"),
        SilkTabItem(title: "标签4"),
        SilkTabItem(title: "标签5"),
        SilkTabItem(title: "标签6"),
        SilkTabItem(title: "标签7"),
        SilkTabItem(title: "标签8")
    ]

    /**
     * 基础标签页内容构建器
     */
    func BasicTabContent(index: Int32): (CustomView) -> ViewBuilder {
        { _ => AppLog.info("===222===${index}"); _basicTabContentBuilder(this, ObservedProperty("", index))}
//        bind<Int32>(_basicTabContentBuilder, this)
    }
    @Builder
    private func _basicTabContentBuilder(index: Int32) {
        Column() {
            if (index == 0) {
                Text("这是标签1的内容")
                    .fontSize(16.vp)
                    .fontColor(Color.BLACK)
                    .padding(20.vp)
            } else if (index == 1) {
                Text("这是标签2的内容")
                    .fontSize(16.vp)
                    .fontColor(Color.BLUE)
                    .padding(20.vp)
            } else if (index == 2) {
                Text("这是标签3的内容")
                    .fontSize(16.vp)
                    .fontColor(Color.GREEN)
                    .padding(20.vp)
            } else if (index == 3) {
                Text("这是标签4的内容")
                    .fontSize(16.vp)
                    .fontColor(Color.RED)
                    .padding(20.vp)
            } else {
                Text("默认内容")
                    .fontSize(16.vp)
                    .fontColor(Color.GRAY)
                    .padding(20.vp)
            }
        }
        .width(100.percent)
        .height(100.vp)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor(Color.WHITE)
    }
    /**
     * 卡片类型标签页内容构建器
     */
    func CardTabContent(index: Int32): (CustomView) -> ViewBuilder {
        {_ => _cardTabContent(this, ObservedProperty("", index))}
//        bind<Int32>(_cardTabContent, this)
    }

    @Builder
    private func _cardTabContent(index: Int32) {
        Column() {
            Text("卡片类型标签页内容 ${index + 1}")
                .fontSize(16.vp)
                .fontColor(Color.BLACK)
                .padding(20.vp)
        }
        .width(100.percent)
        .height(80.vp)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .backgroundColor(Color(0xF5F5F5))
    }

    func build() {
        Column() {
            Nav("Tabs 标签页")
            Scroll() {
                Column() {
                    // 基础用法
                    Title("基础用法")
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(active: activeTab1),
                        Content: BasicTabContent,
                        hasContent: true,
                        onTabChange: { name, index => activeTab1 = index }
                    )

                    // 标签页类型
                    Title("标签页类型")
                    Text("线条类型（默认）")
                        .fontSize(14.vp)
                        .fontColor(Color.GRAY)
                        .margin(bottom: 10.vp)
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(
                            tabsType: SilkTabsType.LINE,
                            active: activeTab2
                        ),
                        Content: {i: Int32 => CardTabContent(i)},
                        hasContent: true,
                        onTabChange: { name, index => activeTab2 = index }
                    )

                    Text("卡片类型")
                        .fontSize(14.vp)
                        .fontColor(Color.GRAY)
                        .margin(top: 20.vp, bottom: 10.vp)
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(
                            tabsType: SilkTabsType.CARD,
                            active: activeTab2
                        ),
                        onTabChange: { name, index => activeTab2 = index }
                    )

                    // 徽标提示
                    Title("徽标提示")
                    SilkTabs(
                        tabs: badgeTabs,
                        props: SilkTabsOptions(active: activeTab3),
                        onTabChange: { name, index => activeTab3 = index }
                    )

                    // 禁用状态
                    Title("禁用状态")
                    SilkTabs(
                        tabs: disabledTabs,
                        props: SilkTabsOptions(active: activeTab4),
                        onTabChange: { name, index => activeTab4 = index }
                    )

                    // 滚动导航
                    Title("滚动导航")
                    SilkTabs(
                        tabs: manyTabs,
                        props: SilkTabsOptions(
                            shrink: true,
                            swipeThreshold: 4
                        )
                    )

                    // 自定义颜色
                    Title("自定义颜色")
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(
                            color: Color.RED,
                            titleActiveColor: Color.RED,
                            titleInactiveColor: Color.GRAY
                        )
                    )

                    // 动画效果
                    Title("动画效果")
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(
                            animated: true,
                            duration: 500
                        )
                    )

                    // 自定义样式
                    Title("自定义样式")
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(
                            lineWidth: 60.vp,
                            lineHeight: 4.vp,
                            color: Color.RED
                        )
                    )

                    // 隐藏标签栏
                    Title("隐藏标签栏")
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(
                            showHeader: false
                        ),
                        Content: BasicTabContent,
                        hasContent: true
                    )

                    // 切换事件
                    Title("切换事件")
                    Text("当前激活标签: ${activeTab1 + 1}")
                        .fontSize(14.vp)
                        .fontColor(Color.BLUE)
                        .margin(bottom: 10.vp)
                    SilkTabs(
                        tabs: basicTabs,
                        props: SilkTabsOptions(active: activeTab1),
                        onTabClick: { tab, index =>
                            // 可以在这里处理标签点击事件
                        },
                        onTabChange: { name, index =>
                            activeTab1 = index
                            // 可以在这里处理标签切换事件
                        }
                    )
                }
                .padding(left: 16.vp, right: 16.vp, bottom: 20.vp)
                .alignItems(HorizontalAlign.Start)
            }
            .width(100.percent)
            .height(100.percent)
        }
        .width(100.percent)
        .height(100.percent)
        .backgroundColor(Color.WHITE)
    }
}
