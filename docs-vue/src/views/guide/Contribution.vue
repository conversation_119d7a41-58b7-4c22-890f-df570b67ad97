<template>
  <div class="content">
    <h1>贡献指南</h1>
    <p>感谢你考虑为 SilkUI 贡献代码！</p>
    
    <h2>开发环境</h2>
    <p>首先，你需要设置开发环境：</p>
    <pre><code class="language-bash"># 克隆仓库
git clone https://github.com/yourusername/silkui.git

# 安装依赖
cd silkui
npm install

# 启动开发服务器
npm run dev</code></pre>
    
    <h2>开发规范</h2>
    <p>在开发过程中，请遵循以下规范：</p>
    <ul>
      <li>遵循代码风格规范</li>
      <li>编写单元测试</li>
      <li>保持代码简洁</li>
      <li>提交前进行代码检查</li>
    </ul>
    
    <h2>提交 Pull Request</h2>
    <p>提交 Pull Request 前，请确保：</p>
    <ul>
      <li>代码通过所有测试</li>
      <li>更新相关文档</li>
      <li>遵循提交信息规范</li>
    </ul>
    
    <h2>报告问题</h2>
    <p>如果你发现了问题，请在 GitHub 上提交 Issue，并提供以下信息：</p>
    <ul>
      <li>问题描述</li>
      <li>复现步骤</li>
      <li>期望行为</li>
      <li>实际行为</li>
      <li>环境信息</li>
    </ul>
  </div>
</template>

<script setup>
// 这里可以添加页面逻辑
</script>

<style lang="scss" scoped>
// 这里可以添加页面样式
</style>
