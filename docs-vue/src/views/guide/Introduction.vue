<template>
  <div class="content">
    <h1>介绍</h1>
    <p>SilkUI 是一个基于 Cangjie 的移动端组件库，提供了丰富的基础组件和业务组件，帮助开发者快速搭建移动应用。</p>
    
    <h2>特性</h2>
    <ul>
      <li><strong>轻量</strong>：组件体积小，性能高</li>
      <li><strong>可靠</strong>：经过严格测试，稳定可靠</li>
      <li><strong>易用</strong>：API 设计简洁，使用方便</li>
      <li><strong>可定制</strong>：支持主题定制，满足个性化需求</li>
    </ul>
    
    <h2>安装</h2>
    <pre><code class="language-bash">npm install silkui</code></pre>
    
    <h2>快速上手</h2>
    <p>请参考 <router-link to="/guide/quickstart">快速上手</router-link> 章节。</p>
    
    <h2>浏览器支持</h2>
    <p>SilkUI 支持现代浏览器和 Android、iOS 等移动端设备。</p>
    
    <h2>版本</h2>
    <p>当前版本：1.0.0</p>
    
    <h2>贡献</h2>
    <p>如果您有任何问题或建议，欢迎提交 Issue 或 Pull Request。</p>
  </div>
</template>

<script setup>
// 这里可以添加页面逻辑
</script>

<style lang="scss" scoped>
// 这里可以添加页面样式
</style>
