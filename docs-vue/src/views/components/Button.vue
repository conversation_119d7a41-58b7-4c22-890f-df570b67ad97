<template>
  <div class="content">
    <h1>Button 按钮</h1>
    <p>按钮用于触发一个操作，如提交表单。</p>

    <h2>引入</h2>
    <pre><code class="language-js">import { SilkButton } from 'silkui';</code></pre>

    <h2>代码演示</h2>

    <h3>按钮类型</h3>
    <p>按钮支持 <code>default</code>、<code>primary</code>、<code>success</code>、<code>warning</code>、<code>danger</code> 五种类型，默认为 <code>default</code>。</p>
    <div class="demo">
      <div class="demo-row">
        <silk-button text="默认按钮" />
        <silk-button type="primary" text="主要按钮" />
        <silk-button type="success" text="成功按钮" />
        <silk-button type="warning" text="警告按钮" />
        <silk-button type="danger" text="危险按钮" />
      </div>
      <div class="code-block">
        <pre><code class="language-html">&lt;silk-button text="默认按钮" /&gt;
&lt;silk-button type="primary" text="主要按钮" /&gt;
&lt;silk-button type="success" text="成功按钮" /&gt;
&lt;silk-button type="warning" text="警告按钮" /&gt;
&lt;silk-button type="danger" text="危险按钮" /&gt;</code></pre>
      </div>
    </div>

    <h3>朴素按钮</h3>
    <p>通过 <code>plain</code> 属性将按钮设置为朴素按钮，朴素按钮的文字为按钮颜色，背景为白色。</p>
    <div class="demo">
      <div class="demo-row">
        <silk-button plain type="primary" text="朴素按钮" />
        <silk-button plain type="success" text="朴素按钮" />
      </div>
      <div class="code-block">
        <pre><code class="language-html">&lt;silk-button plain type="primary" text="朴素按钮" /&gt;
&lt;silk-button plain type="success" text="朴素按钮" /&gt;</code></pre>
      </div>
    </div>

    <h3>禁用状态</h3>
    <p>通过 <code>disabled</code> 属性来禁用按钮，禁用状态下按钮不可点击。</p>
    <div class="demo">
      <div class="demo-row">
        <silk-button disabled text="禁用状态" />
        <silk-button disabled type="primary" text="禁用状态" />
        <silk-button disabled type="success" text="禁用状态" />
      </div>
      <div class="code-block">
        <pre><code class="language-html">&lt;silk-button disabled text="禁用状态" /&gt;
&lt;silk-button disabled type="primary" text="禁用状态" /&gt;
&lt;silk-button disabled type="success" text="禁用状态" /&gt;</code></pre>
      </div>
    </div>

    <h3>按钮尺寸</h3>
    <p>支持 <code>large</code>、<code>normal</code>、<code>small</code>、<code>mini</code> 四种尺寸，默认为 <code>normal</code>。</p>
    <div class="demo">
      <div class="demo-row">
        <silk-button size="large" type="primary" text="大号按钮" />
        <silk-button type="primary" text="普通按钮" />
        <silk-button size="small" type="primary" text="小型按钮" />
        <silk-button size="mini" type="primary" text="迷你按钮" />
      </div>
      <div class="code-block">
        <pre><code class="language-html">&lt;silk-button size="large" type="primary" text="大号按钮" /&gt;
&lt;silk-button type="primary" text="普通按钮" /&gt;
&lt;silk-button size="small" type="primary" text="小型按钮" /&gt;
&lt;silk-button size="mini" type="primary" text="迷你按钮" /&gt;</code></pre>
      </div>
    </div>

    <h2>API</h2>

    <h3>Props</h3>
    <table>
      <thead>
        <tr>
          <th>参数</th>
          <th>说明</th>
          <th>类型</th>
          <th>默认值</th>
          <th>装饰器</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>text</td>
          <td>按钮文字</td>
          <td><em>string</em></td>
          <td>-</td>
          <td>@Prop</td>
        </tr>
        <tr>
          <td>type</td>
          <td>类型，可选值为 <code>primary</code> <code>success</code> <code>warning</code> <code>danger</code></td>
          <td><em>string</em></td>
          <td><code>default</code></td>
          <td>@Prop</td>
        </tr>
        <tr>
          <td>size</td>
          <td>尺寸，可选值为 <code>large</code> <code>small</code> <code>mini</code></td>
          <td><em>string</em></td>
          <td><code>normal</code></td>
          <td>@Prop</td>
        </tr>
        <tr>
          <td>plain</td>
          <td>是否为朴素按钮</td>
          <td><em>boolean</em></td>
          <td><code>false</code></td>
          <td>@Prop</td>
        </tr>
        <tr>
          <td>disabled</td>
          <td>是否禁用按钮</td>
          <td><em>boolean</em></td>
          <td><code>false</code></td>
          <td>@Prop</td>
        </tr>
        <tr>
          <td>loading</td>
          <td>是否显示加载状态</td>
          <td><em>boolean</em></td>
          <td><code>false</code></td>
          <td>@Prop</td>
        </tr>
        <tr>
          <td>icon</td>
          <td>图标名称</td>
          <td><em>string</em></td>
          <td>-</td>
          <td>@Prop</td>
        </tr>
        <tr>
          <td>iconPosition</td>
          <td>图标位置，可选值为 <code>right</code></td>
          <td><em>string</em></td>
          <td><code>left</code></td>
          <td>@Prop</td>
        </tr>
      </tbody>
    </table>

    <h3>Events</h3>
    <table>
      <thead>
        <tr>
          <th>事件名</th>
          <th>说明</th>
          <th>回调参数</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>click</td>
          <td>点击按钮时触发</td>
          <td><em>event: Event</em></td>
        </tr>
      </tbody>
    </table>

    <h2>主题定制</h2>
    <p>参考 <router-link to="/guide/theme">主题定制</router-link> 章节。</p>
  </div>
</template>

<script setup>
// 这里可以导入 SilkButton 组件
// import { SilkButton } from 'silkui';
</script>

<style lang="scss" scoped>
.demo-row {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
}

.code-block {
  background-color: var(--code-background);
  border-radius: 4px;
  overflow: hidden;
}
</style>
